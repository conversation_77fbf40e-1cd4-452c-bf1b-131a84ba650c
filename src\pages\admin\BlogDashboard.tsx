import React, { useEffect, useState } from 'react'
import { Link } from 'react-router-dom'
import { useAuth } from '@/contexts/AuthContext'
import ProtectedRoute from '@/components/auth/ProtectedRoute'
import AdminLayout from '@/components/layout/AdminLayout'

import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { getBlogPosts, deleteBlogPost, updateBlogPost, BlogPost, getDisplayAuthor } from '@/lib/supabase'
import BlogSubmissionDialog from '@/components/BlogSubmissionDialog'
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Eye,
  Calendar,
  User,
  FileText,
  BarChart3,
  Archive,
  ArchiveRestore,
  Clock,
  Check,
  X,
  Send,
  Globe
} from 'lucide-react'
import { toast } from 'sonner'
import UpgradePrompt from '@/components/UpgradePrompt'
import { useOrganization } from '@/contexts/OrganizationContext'

const BlogDashboard = () => {
  const { user, profile } = useAuth()
  const { currentOrganization } = useOrganization()
  const [posts, setPosts] = useState<BlogPost[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filteredPosts, setFilteredPosts] = useState<BlogPost[]>([])
  const [contentFilter, setContentFilter] = useState<'all' | 'blog' | 'testimonials'>('all')
  const [showSubmissionDialog, setShowSubmissionDialog] = useState(false)
  const [selectedPostForSubmission, setSelectedPostForSubmission] = useState<BlogPost | null>(null)

  // Check for author filter in URL params
  const urlParams = new URLSearchParams(window.location.search)
  const authorFilter = urlParams.get('author')

  const fetchPosts = async () => {
    if (!user?.id || !currentOrganization?.id) return

    try {
      // Fetch posts for the current organization only
      // Super admins can see all posts, others see only their organization's posts
      const organizationId = profile?.role === 'super_admin' ? undefined : currentOrganization.id

      const { data, error } = await getBlogPosts(undefined, true, organizationId, false)
      if (error) {
        toast.error('Failed to load blog posts')
        console.error('Error fetching posts:', error)
      } else {
        let postsData = data || []

        // For editors, only show their own posts
        // For admins and above within the organization, show all organization posts or filter by author if specified
        if (profile?.role === 'editor') {
          postsData = postsData.filter(post => post.author_id === user.id)
        } else if (authorFilter) {
          // Admin+ users can filter by specific author within their organization
          postsData = postsData.filter(post => post.author_id === authorFilter)
        }

        setPosts(postsData)
        setFilteredPosts(postsData)
      }
    } catch (error) {
      toast.error('An unexpected error occurred')
      console.error('Error:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchPosts()
  }, [authorFilter, user?.id, profile?.role])

  useEffect(() => {
    let filtered = posts

    // Apply content filter
    if (contentFilter === 'blog') {
      filtered = filtered.filter(post =>
        post.category !== 'testimonials' &&
        (!post.tags || !post.tags.includes('testimonial'))
      )
    } else if (contentFilter === 'testimonials') {
      filtered = filtered.filter(post =>
        post.category === 'testimonials' ||
        (post.tags && post.tags.includes('testimonial'))
      )
    }

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(post =>
        post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        post.excerpt?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    setFilteredPosts(filtered)
  }, [searchTerm, posts, contentFilter])

  const handleDeletePost = async (postId: string, postTitle: string) => {
    if (!confirm(`Are you sure you want to delete "${postTitle}"?`)) {
      return
    }

    try {
      const { error } = await deleteBlogPost(postId)
      if (error) {
        toast.error('Failed to delete post')
        console.error('Error deleting post:', error)
      } else {
        toast.success('Post deleted successfully')
        setPosts(posts.filter(post => post.id !== postId))
      }
    } catch (error) {
      toast.error('An unexpected error occurred')
      console.error('Error:', error)
    }
  }

  const handleArchive = async (postId: string, postTitle: string) => {
    if (!confirm(`Are you sure you want to archive "${postTitle}"?`)) {
      return
    }

    try {
      const { data, error } = await updateBlogPost(postId, { status: 'archived' })
      if (error) {
        toast.error('Failed to archive post')
        console.error('Error archiving post:', error)
      } else {
        toast.success('Post archived successfully')
        // Update local state immediately
        setPosts(prev => prev.map(post =>
          post.id === postId ? { ...post, status: 'archived' as const } : post
        ))
        setFilteredPosts(prev => prev.map(post =>
          post.id === postId ? { ...post, status: 'archived' as const } : post
        ))
      }
    } catch (error) {
      toast.error('An unexpected error occurred')
      console.error('Error:', error)
    }
  }

  const handleUnpublish = async (postId: string, postTitle: string) => {
    if (!confirm(`Are you sure you want to unpublish "${postTitle}"?`)) {
      return
    }

    try {
      const { data, error } = await updateBlogPost(postId, { status: 'draft' })
      if (error) {
        toast.error('Failed to unpublish post')
        console.error('Error unpublishing post:', error)
      } else {
        toast.success('Post unpublished successfully')
        // Update local state immediately
        setPosts(prev => prev.map(post =>
          post.id === postId ? { ...post, status: 'draft' as const } : post
        ))
        setFilteredPosts(prev => prev.map(post =>
          post.id === postId ? { ...post, status: 'draft' as const } : post
        ))
      }
    } catch (error) {
      toast.error('An unexpected error occurred')
      console.error('Error:', error)
    }
  }

  const handleRestore = async (postId: string, postTitle: string) => {
    if (!confirm(`Are you sure you want to restore "${postTitle}" to draft?`)) {
      return
    }

    try {
      const { data, error } = await updateBlogPost(postId, { status: 'draft' })
      if (error) {
        toast.error('Failed to restore post')
        console.error('Error restoring post:', error)
      } else {
        toast.success('Post restored to draft successfully')
        // Update local state immediately
        setPosts(prev => prev.map(post =>
          post.id === postId ? { ...post, status: 'draft' as const } : post
        ))
        setFilteredPosts(prev => prev.map(post =>
          post.id === postId ? { ...post, status: 'draft' as const } : post
        ))
      }
    } catch (error) {
      toast.error('An unexpected error occurred')
      console.error('Error:', error)
    }
  }

  const handleApprove = async (postId: string, postTitle: string) => {
    if (!confirm(`Are you sure you want to approve and publish "${postTitle}"?`)) {
      return
    }

    try {
      const { data, error } = await updateBlogPost(postId, {
        status: 'published',
        published_at: new Date().toISOString()
      })
      if (error) {
        toast.error('Failed to approve post')
        console.error('Error approving post:', error)
      } else {
        toast.success('Post approved and published successfully')
        // Update local state immediately
        setPosts(prev => prev.map(post =>
          post.id === postId ? { ...post, status: 'published' as const, published_at: new Date().toISOString() } : post
        ))
        setFilteredPosts(prev => prev.map(post =>
          post.id === postId ? { ...post, status: 'published' as const, published_at: new Date().toISOString() } : post
        ))
      }
    } catch (error) {
      toast.error('An unexpected error occurred')
      console.error('Error:', error)
    }
  }

  const handleReject = async (postId: string, postTitle: string) => {
    if (!confirm(`Are you sure you want to reject "${postTitle}"? The author can revise and resubmit.`)) {
      return
    }

    try {
      const { data, error } = await updateBlogPost(postId, { status: 'rejected' })
      if (error) {
        toast.error('Failed to reject post')
        console.error('Error rejecting post:', error)
      } else {
        toast.success('Post rejected - author can revise and resubmit')
        // Update local state immediately
        setPosts(prev => prev.map(post =>
          post.id === postId ? { ...post, status: 'rejected' as const } : post
        ))
        setFilteredPosts(prev => prev.map(post =>
          post.id === postId ? { ...post, status: 'rejected' as const } : post
        ))
      }
    } catch (error) {
      toast.error('An unexpected error occurred')
      console.error('Error:', error)
    }
  }

  const handleSubmitToMBI = (post: BlogPost) => {
    setSelectedPostForSubmission(post)
    setShowSubmissionDialog(true)
  }

  const handleSubmissionSuccess = () => {
    // Refresh the posts to show updated submission status
    fetchPosts()
  }



  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'draft':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
      case 'pending_approval':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case 'rejected':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      case 'archived':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
    }
  }

  const stats = {
    total: posts.length,
    published: posts.filter(p => p.status === 'published').length,
    drafts: posts.filter(p => p.status === 'draft').length,
    pending: posts.filter(p => p.status === 'pending_approval').length,
    archived: posts.filter(p => p.status === 'archived').length
  }

  const title = (profile?.role === 'editor') ? 'My Blog' : 'Blog Dashboard'
  const subtitle = authorFilter
    ? `Showing posts by ${posts[0]?.author?.full_name || 'selected author'}`
    : `Welcome back, ${profile?.full_name || profile?.email}`

  const headerActions = (
    <div className="flex items-center gap-2 flex-wrap">
      {authorFilter && (
        <Button
          variant="outline"
          size="sm"
          className="text-xs sm:text-sm"
          onClick={() => window.location.href = '/admin/blog'}
        >
          Clear Filter
        </Button>
      )}
    </div>
  )

  return (
    <ProtectedRoute requiredRole="user">
      <AdminLayout
        title={title}
        subtitle={subtitle}
        actions={headerActions}
      >
        <div>

        {/* Stats Cards */}
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4 sm:gap-6 mb-8">
          <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all duration-300">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Posts</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total}</div>
            </CardContent>
          </Card>

          <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all duration-300">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Published</CardTitle>
              <BarChart3 className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{stats.published}</div>
            </CardContent>
          </Card>

          <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all duration-300">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Drafts</CardTitle>
              <Edit className="h-4 w-4 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">{stats.drafts}</div>
            </CardContent>
          </Card>

          <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all duration-300">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending Approval</CardTitle>
              <Clock className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{stats.pending}</div>
            </CardContent>
          </Card>

          <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all duration-300">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Archived</CardTitle>
              <Trash2 className="h-4 w-4 text-gray-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-600">{stats.archived}</div>
            </CardContent>
          </Card>
        </div>

        {/* AI Upgrade Prompt */}
        {currentOrganization && (
          <UpgradePrompt
            type="ai"
            currentUsage={currentOrganization.ai_credits_used}
            limit={currentOrganization.ai_credits_limit}
            className="mb-6"
          />
        )}

        {/* Actions Bar */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <div className="flex flex-col sm:flex-row gap-4 flex-1">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                type="text"
                placeholder="Search posts..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Content Type Filter */}
            <Select value={contentFilter} onValueChange={(value: 'all' | 'blog' | 'testimonials') => setContentFilter(value)}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Content</SelectItem>
                <SelectItem value="blog">Blog Posts Only</SelectItem>
                <SelectItem value="testimonials">Success Stories Only</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex gap-2">
            <Link to="/admin/blog/testimonial">
              <Button variant="outline" className="border-primary text-primary hover:bg-primary hover:text-white">
                <Plus className="h-4 w-4 mr-2" />
                Share Your Story
              </Button>
            </Link>
            <Link to="/admin/blog/new">
              <Button className="bg-gradient-to-r from-primary to-accent text-white">
                <Plus className="h-4 w-4 mr-2" />
                New Post
              </Button>
            </Link>
          </div>
        </div>

        {/* Posts List */}
        <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-sm">
            <CardHeader>
              <CardTitle>
                {(profile?.role === 'editor') ? 'My Posts' : 'All Posts'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="space-y-4">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="animate-pulse">
                      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"></div>
                      <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
                    </div>
                  ))}
                </div>
              ) : filteredPosts.length === 0 ? (
                <div className="text-center py-12">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    {searchTerm ? 'No posts found' : 'No posts yet'}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                    {searchTerm 
                      ? 'Try adjusting your search terms.' 
                      : 'Get started by creating your first blog post.'
                    }
                  </p>
                  {!searchTerm && (
                    <Link to="/admin/blog/new">
                      <Button>
                        <Plus className="h-4 w-4 mr-2" />
                        Create First Post
                      </Button>
                    </Link>
                  )}
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredPosts.map((post) => (
                    <div
                      key={post.id}
                      className="flex flex-col sm:flex-row sm:items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors gap-4"
                    >
                      {/* Thumbnail */}
                      {(post.thumbnail_url || post.featured_image) && (
                        <div className="w-16 h-12 flex-shrink-0 overflow-hidden rounded">
                          <img
                            src={post.thumbnail_url || post.featured_image}
                            alt={post.title}
                            className="w-full h-full object-cover"
                          />
                        </div>
                      )}

                      <div className="flex-1 min-w-0">
                        <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-2">
                          <h3 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white truncate">
                            {post.title}
                          </h3>
                          <div className="flex gap-2">
                            <Badge className={getStatusColor(post.status)}>
                              {post.status}
                            </Badge>
                            {post.category === 'testimonials' && (
                              <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800">
                                Testimonial
                              </Badge>
                            )}
                          </div>
                        </div>

                        <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 text-xs sm:text-sm text-gray-500 dark:text-gray-400">
                          <div className="flex items-center gap-1">
                            <Calendar className="h-4 w-4" />
                            {formatDate(post.created_at)}
                          </div>
                          {(() => {
                            const displayAuthor = getDisplayAuthor(post)
                            return (
                              <div className="flex items-center gap-1">
                                <User className="h-4 w-4" />
                                {displayAuthor.name}
                              </div>
                            )
                          })()}
                        </div>
                      </div>

                      <div className="flex items-center gap-2 justify-end sm:ml-4">
                        {post.status === 'published' && (
                          <Link to={`/blog/${post.slug}`} target="_blank">
                            <Button variant="ghost" size="sm" className="text-xs sm:text-sm">
                              <Eye className="h-4 w-4" />
                              <span className="hidden sm:inline ml-1">View</span>
                            </Button>
                          </Link>
                        )}

                        <Link to={`/admin/blog/edit/${post.id}`}>
                          <Button variant="ghost" size="sm" className="text-xs sm:text-sm">
                            <Edit className="h-4 w-4" />
                            <span className="hidden sm:inline ml-1">Edit</span>
                          </Button>
                        </Link>

                        {/* Submit to MBI Blog button - only for published posts that haven't been submitted */}
                        {post.status === 'published' &&
                         (!post.submission_status || post.submission_status === 'private') &&
                         post.organization_id && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleSubmitToMBI(post)}
                            className="text-blue-600 hover:text-blue-700 hover:bg-blue-50 dark:hover:bg-blue-900/20 text-xs sm:text-sm"
                          >
                            <Send className="h-4 w-4" />
                            <span className="hidden sm:inline ml-1">Submit to MBI</span>
                          </Button>
                        )}

                        {/* Show submission status badge */}
                        {post.submission_status && post.submission_status !== 'private' && (
                          <Badge
                            variant="outline"
                            className={`text-xs ${
                              post.submission_status === 'submitted' ? 'bg-blue-50 text-blue-700 border-blue-200' :
                              post.submission_status === 'approved' ? 'bg-green-50 text-green-700 border-green-200' :
                              post.submission_status === 'published_mbi' ? 'bg-purple-50 text-purple-700 border-purple-200' :
                              'bg-red-50 text-red-700 border-red-200'
                            }`}
                          >
                            {post.submission_status === 'submitted' && <Clock className="h-3 w-3 mr-1" />}
                            {post.submission_status === 'approved' && <Check className="h-3 w-3 mr-1" />}
                            {post.submission_status === 'published_mbi' && <Globe className="h-3 w-3 mr-1" />}
                            {post.submission_status === 'rejected' && <X className="h-3 w-3 mr-1" />}
                            {post.submission_status === 'submitted' ? 'Under Review' :
                             post.submission_status === 'approved' ? 'Approved' :
                             post.submission_status === 'published_mbi' ? 'On MBI Blog' :
                             'Rejected'}
                          </Badge>
                        )}

                        {/* Status-specific actions */}
                        {post.status === 'published' && (
                          <>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleUnpublish(post.id, post.title)}
                              className="text-yellow-600 hover:text-yellow-700 hover:bg-yellow-50 dark:hover:bg-yellow-900/20 text-xs sm:text-sm"
                            >
                              <Edit className="h-4 w-4" />
                              <span className="hidden sm:inline ml-1">Unpublish</span>
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleArchive(post.id, post.title)}
                              className="text-gray-600 hover:text-gray-700 hover:bg-gray-50 dark:hover:bg-gray-900/20 text-xs sm:text-sm"
                            >
                              <Archive className="h-4 w-4" />
                              <span className="hidden sm:inline ml-1">Archive</span>
                            </Button>
                          </>
                        )}

                        {post.status === 'pending_approval' && (profile?.role && ['admin', 'super_admin', 'owner'].includes(profile.role)) && (
                          <>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleApprove(post.id, post.title)}
                              className="text-green-600 hover:text-green-700 hover:bg-green-50 dark:hover:bg-green-900/20 text-xs sm:text-sm"
                            >
                              <Check className="h-4 w-4" />
                              <span className="hidden sm:inline ml-1">Approve</span>
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleReject(post.id, post.title)}
                              className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20 text-xs sm:text-sm"
                            >
                              <X className="h-4 w-4" />
                              <span className="hidden sm:inline ml-1">Reject</span>
                            </Button>
                          </>
                        )}

                        {post.status === 'archived' && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRestore(post.id, post.title)}
                            className="text-blue-600 hover:text-blue-700 hover:bg-blue-50 dark:hover:bg-blue-900/20 text-xs sm:text-sm"
                          >
                            <ArchiveRestore className="h-4 w-4" />
                            <span className="hidden sm:inline ml-1">Restore</span>
                          </Button>
                        )}

                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeletePost(post.id, post.title)}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20 text-xs sm:text-sm"
                        >
                          <Trash2 className="h-4 w-4" />
                          <span className="hidden sm:inline ml-1">Delete</span>
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Blog Submission Dialog */}
        <BlogSubmissionDialog
          isOpen={showSubmissionDialog}
          onClose={() => setShowSubmissionDialog(false)}
          post={selectedPostForSubmission}
          onSubmissionSuccess={handleSubmissionSuccess}
        />
      </AdminLayout>
    </ProtectedRoute>
  )
}

export default BlogDashboard
