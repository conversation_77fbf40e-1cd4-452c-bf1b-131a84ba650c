import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
})

// Database types
export interface Profile {
  id: string
  email: string
  role: 'saas_owner' | 'super_admin' | 'admin' | 'editor' | 'viewer'
  full_name?: string
  avatar_url?: string
  created_at: string
}

export interface BlogPost {
  id: string
  title: string
  slug: string
  content: any // JSON content from Tiptap
  excerpt?: string
  featured_image?: string
  thumbnail_url?: string // For Medium-style thumbnails
  status: 'draft' | 'pending_approval' | 'published' | 'archived' | 'rejected'
  author_id: string
  author?: Profile
  author_display?: 'real_name' | 'anonymous' | 'mbi_team' | 'custom_name' // Author display preference
  author_option?: 'use_name' | 'anonymous' | 'mbi_team' | 'custom_name' // For form compatibility
  custom_name?: string // Custom name when author_display is 'custom_name'
  published_at?: string
  created_at: string
  updated_at: string
  categories?: BlogCategory[]
  tags?: string[] // For categorizing testimonials and other content
  // Hybrid blog system fields
  organization_id?: string
  submission_status?: 'private' | 'submitted' | 'approved' | 'rejected' | 'published_mbi'
  submitted_for_approval?: boolean
  submitted_at?: string
  approved_by?: string
  approved_at?: string
  rejection_reason?: string
  original_organization_id?: string
  is_mbi_featured?: boolean
  category?: string // Single category field
}

export interface BlogCategory {
  id: string
  name: string
  slug: string
  created_at: string
}

export interface EmailIntegration {
  id: string
  user_id: string
  name: string
  provider: 'gmail' | 'outlook' | 'resend' | 'postmark' | 'sendgrid' | 'mailgun' | 'smtp'
  config: Record<string, any>
  is_active: boolean
  is_default: boolean
  created_at: string
  updated_at: string
}

export interface EmailTemplate {
  id: string
  user_id: string
  integration_id: string
  name: string
  subject: string
  body: string
  variables: string[]
  is_default: boolean
  created_at: string
  updated_at: string
}

export interface NewsletterSubscriber {
  id: string
  email: string
  name?: string
  status: 'active' | 'unsubscribed' | 'bounced'
  source: 'website' | 'blog' | 'quote_form' | 'manual'
  subscribed_at: string
  unsubscribed_at?: string
  metadata: Record<string, any>
  created_at: string
  updated_at: string
}

// Auth helpers
export const signInWithGoogle = async (redirectTo?: string) => {
  const { data, error } = await supabase.auth.signInWithOAuth({
    provider: 'google',
    options: {
      redirectTo: redirectTo || `${window.location.origin}/admin/blog`,
      queryParams: {
        access_type: 'offline',
        prompt: 'consent',
      },
    }
  })
  return { data, error }
}

export const signInWithEmail = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  })
  return { data, error }
}

export const signUpWithEmail = async (email: string, password: string, fullName?: string) => {
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: {
        full_name: fullName,
      }
    }
  })
  return { data, error }
}

export const signOut = async () => {
  const { error } = await supabase.auth.signOut()
  return { error }
}

export const getCurrentUser = async () => {
  const { data: { user }, error } = await supabase.auth.getUser()
  return { user, error }
}

export const getUserProfile = async (userId: string) => {
  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', userId)
    .single()

  return { data, error }
}

export const createOrUpdateProfile = async (user: any) => {
  // Check if this is Stephen's email for SaaS owner role
  const isSaasOwner = user.email === '<EMAIL>'

  const { data, error } = await supabase
    .from('profiles')
    .upsert({
      id: user.id,
      email: user.email,
      full_name: user.user_metadata?.full_name || user.email,
      avatar_url: user.user_metadata?.avatar_url,
      role: isSaasOwner ? 'saas_owner' : 'viewer' // Default role for new users
    })
    .select()
    .single()

  return { data, error }
}

// Profile management functions
export const updateProfile = async (userId: string, updates: Partial<Profile>) => {
  const { data, error } = await supabase
    .from('profiles')
    .update(updates)
    .eq('id', userId)
    .select()
    .single()

  return { data, error }
}

export const getAllUsers = async () => {
  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .order('created_at', { ascending: false })

  return { data, error }
}

export const deleteUser = async (userId: string) => {
  const { error } = await supabase
    .from('profiles')
    .delete()
    .eq('id', userId)

  return { error }
}

export const updateUserRole = async (userId: string, role: Profile['role']) => {
  const { data, error } = await supabase
    .from('profiles')
    .update({ role })
    .eq('id', userId)
    .select()
    .single()

  return { data, error }
}

// Platform Owner Admin Functions
export const updateUserSubscription = async (userId: string, subscriptionPlan: string) => {
  const { data, error } = await supabase
    .from('profiles')
    .update({ subscription_plan: subscriptionPlan })
    .eq('id', userId)
    .select()

  return { data, error }
}

export const updateOrganizationSubscription = async (orgId: string, subscriptionPlan: string, creditsData?: {
  workflow_credits_limit?: number
  ai_credits_limit?: number
}) => {
  const updateData: any = { subscription_plan: subscriptionPlan }

  // Set credit limits based on plan if not provided
  if (!creditsData) {
    switch (subscriptionPlan) {
      case 'free':
        updateData.workflow_credits_limit = 100
        updateData.ai_credits_limit = 50
        break
      case 'basic':
        updateData.workflow_credits_limit = 1000
        updateData.ai_credits_limit = 500
        break
      case 'pro':
        updateData.workflow_credits_limit = 5000
        updateData.ai_credits_limit = 2000
        break
      case 'enterprise':
        updateData.workflow_credits_limit = 50000
        updateData.ai_credits_limit = 10000
        break
    }
  } else {
    Object.assign(updateData, creditsData)
  }

  const { data, error } = await supabase
    .from('organizations')
    .update(updateData)
    .eq('id', orgId)
    .select()

  return { data, error }
}

export const addCreditsToOrganization = async (orgId: string, workflowCredits: number = 0, aiCredits: number = 0) => {
  // First get current limits
  const { data: org, error: fetchError } = await supabase
    .from('organizations')
    .select('workflow_credits_limit, ai_credits_limit')
    .eq('id', orgId)
    .single()

  if (fetchError) return { error: fetchError }

  const { data, error } = await supabase
    .from('organizations')
    .update({
      workflow_credits_limit: (org.workflow_credits_limit || 0) + workflowCredits,
      ai_credits_limit: (org.ai_credits_limit || 0) + aiCredits
    })
    .eq('id', orgId)
    .select()

  return { data, error }
}

export const resetOrganizationCredits = async (orgId: string) => {
  const { data, error } = await supabase
    .from('organizations')
    .update({
      workflow_credits_used: 0,
      ai_credits_used: 0,
      credits_reset_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days from now
    })
    .eq('id', orgId)
    .select()

  return { data, error }
}

export const getUserOrganizations = async (userId: string) => {
  const { data, error } = await supabase
    .from('organization_members')
    .select(`
      organization:organizations(*)
    `)
    .eq('user_id', userId)
    .eq('is_active', true)

  return { data, error }
}

export const resetUserPassword = async (email: string) => {
  const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
    redirectTo: `${window.location.origin}/admin/blog`,
  })

  return { data, error }
}

// Blog post helpers
export const getBlogPosts = async (
  status?: string,
  includeTestimonials: boolean = false,
  organizationId?: string,
  isPublicView: boolean = false
) => {
  let query = supabase
    .from('blog_posts')
    .select(`
      *,
      author:profiles!author_id(id, email, full_name, avatar_url)
    `)
    .order('created_at', { ascending: false })

  if (status) {
    query = query.eq('status', status)
  }

  // For public blog view, only show published posts (no organization filter)
  if (isPublicView) {
    query = query.eq('status', 'published')
  } else if (organizationId) {
    // For admin/dashboard views, filter by organization
    query = query.eq('organization_id', organizationId)
  }

  // Exclude testimonials from main blog feed unless specifically requested
  if (!includeTestimonials) {
    query = query.neq('category', 'testimonials')
  }

  const { data, error } = await query

  // Debug logging for troubleshooting
  console.log('getBlogPosts debug:', {
    status,
    includeTestimonials,
    organizationId,
    isPublicView,
    queryResult: {
      dataCount: data?.length || 0,
      error: error?.message || null,
      sampleTitles: data?.slice(0, 3).map(p => p.title) || []
    }
  })

  return { data, error }
}

export const getBlogPost = async (slug: string) => {
  const { data, error } = await supabase
    .from('blog_posts')
    .select(`
      *,
      author:profiles!author_id(id, email, full_name, avatar_url),
      categories:blog_post_categories(
        category:blog_categories(id, name, slug)
      )
    `)
    .eq('slug', slug)
    .single()

  console.log('getBlogPost result:', data)
  return { data, error }
}

export const getBlogPostById = async (id: string) => {
  const { data, error } = await supabase
    .from('blog_posts')
    .select(`
      *,
      author:profiles!author_id(id, email, full_name, avatar_url),
      categories:blog_post_categories(
        category:blog_categories(id, name, slug)
      )
    `)
    .eq('id', id)
    .single()

  console.log('getBlogPostById result:', data)
  return { data, error }
}

export const createBlogPost = async (post: Partial<BlogPost>) => {
  // Convert author_option to author_display for database storage
  const postData = { ...post }
  if (postData.author_option) {
    // Map form values to database values
    const authorOptionMap: Record<string, string> = {
      'use_name': 'real_name',
      'anonymous': 'anonymous',
      'custom_name': 'custom_name',
      'mbi_team': 'mbi_team'
    }

    postData.author_display = authorOptionMap[postData.author_option] || 'real_name'
    delete postData.author_option
  }

  const { data, error } = await supabase
    .from('blog_posts')
    .insert([postData])
    .select()
    .single()

  return { data, error }
}

export const updateBlogPost = async (id: string, updates: Partial<BlogPost>) => {
  const { data, error } = await supabase
    .from('blog_posts')
    .update({ ...updates, updated_at: new Date().toISOString() })
    .eq('id', id)
    .select()
    .single()

  return { data, error }
}

export const deleteBlogPost = async (id: string) => {
  const { error } = await supabase
    .from('blog_posts')
    .delete()
    .eq('id', id)

  return { error }
}

// Get testimonials from blog posts
export const getTestimonials = async () => {
  const { data, error } = await supabase
    .from('blog_posts')
    .select(`
      *,
      author:profiles!author_id(id, email, full_name, avatar_url)
    `)
    .eq('status', 'published')
    .or('category.eq.testimonials,tags.cs.{testimonial}') // Get posts with category=testimonials OR containing 'testimonial' tag
    .order('is_featured', { ascending: false }) // Featured first
    .order('display_order', { ascending: true }) // Then by display order
    .order('created_at', { ascending: false }) // Then by creation date

  return { data, error }
}

// Get blog post statistics by user
export const getBlogPostStatsByUser = async () => {
  const { data, error } = await supabase
    .from('blog_posts')
    .select(`
      author_id,
      status,
      author:profiles!author_id(id, full_name, email)
    `)

  if (error) return { data: null, error }

  // Group by author and count posts
  const stats = data.reduce((acc: any, post: any) => {
    const authorId = post.author_id
    if (!acc[authorId]) {
      acc[authorId] = {
        author: post.author,
        total: 0,
        published: 0,
        drafts: 0,
        archived: 0
      }
    }

    acc[authorId].total++
    if (post.status === 'published') acc[authorId].published++
    else if (post.status === 'draft') acc[authorId].drafts++
    else if (post.status === 'archived') acc[authorId].archived++

    return acc
  }, {})

  return { data: Object.values(stats), error: null }
}

// Get reading time estimate for content
export const getReadingTime = (content: any): number => {
  if (!content) return 0

  // Extract text from Tiptap JSON content
  const extractText = (node: any): string => {
    if (typeof node === 'string') return node
    if (!node || typeof node !== 'object') return ''

    let text = ''
    if (node.text) text += node.text
    if (node.content && Array.isArray(node.content)) {
      text += node.content.map(extractText).join(' ')
    }
    return text
  }

  const text = extractText(content)
  const wordsPerMinute = 200 // Average reading speed
  const wordCount = text.split(/\s+/).filter(word => word.length > 0).length
  return Math.ceil(wordCount / wordsPerMinute)
}

// File upload helper with security validation
export const uploadFile = async (file: File, bucket: string = 'blog-images') => {
  // Import security functions dynamically to avoid circular imports
  const { validateFileUpload, generateSecureFilename } = await import('./security')

  // Validate file security
  const validation = validateFileUpload(file)
  if (!validation.isValid) {
    return { data: null, error: { message: validation.error } }
  }

  // Generate secure filename
  const secureFileName = generateSecureFilename(file.name)
  const filePath = `${new Date().getFullYear()}/${new Date().getMonth() + 1}/${secureFileName}`

  try {
    const { data, error } = await supabase.storage
      .from(bucket)
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false // Prevent overwriting existing files
      })

    if (error) {
      return { data: null, error }
    }

    const { data: { publicUrl } } = supabase.storage
      .from(bucket)
      .getPublicUrl(filePath)

    return { data: { path: filePath, publicUrl }, error: null }
  } catch (error) {
    return { data: null, error: { message: 'Upload failed' } }
  }
}

// Helper function to get display author information
export const getDisplayAuthor = (post: BlogPost) => {
  // Handle cases where author_display might be undefined (for existing posts)
  const authorDisplay = post.author_display || 'real_name'

  switch (authorDisplay) {
    case 'anonymous':
      return {
        name: 'Anonymous',
        avatar_url: null,
        email: null
      }
    case 'mbi_team':
      return {
        name: 'MBI Team',
        avatar_url: null,
        email: null
      }
    case 'custom_name':
      return {
        name: post.custom_name || 'Custom Author',
        avatar_url: null,
        email: null
      }
    case 'real_name':
    default:
      return {
        name: post.author?.full_name || post.author?.email || 'Unknown Author',
        avatar_url: post.author?.avatar_url || null,
        email: post.author?.email || null
      }
  }
}

// Social features functions

// Reactions (supports anonymous users)
export const toggleReaction = async (postId: string, reactionType: string = 'like') => {
  const { data: { user } } = await supabase.auth.getUser()

  if (user) {
    // Authenticated user reaction
    const { data: existing } = await supabase
      .from('blog_reactions')
      .select('id')
      .eq('post_id', postId)
      .eq('user_id', user.id)
      .eq('reaction_type', reactionType)
      .single()

    if (existing) {
      // Remove reaction
      const { error } = await supabase
        .from('blog_reactions')
        .delete()
        .eq('id', existing.id)
      return { data: { action: 'removed' }, error }
    } else {
      // Add reaction
      const { data, error } = await supabase
        .from('blog_reactions')
        .insert({ post_id: postId, user_id: user.id, reaction_type: reactionType })
        .select()
        .single()
      return { data: { action: 'added', ...data }, error }
    }
  } else {
    // Anonymous user reaction
    const anonymousId = generateAnonymousId()

    const { data: existing } = await supabase
      .from('blog_reactions')
      .select('id')
      .eq('post_id', postId)
      .eq('anonymous_id', anonymousId)
      .eq('reaction_type', reactionType)
      .single()

    if (existing) {
      // Remove reaction
      const { error } = await supabase
        .from('blog_reactions')
        .delete()
        .eq('id', existing.id)
      return { data: { action: 'removed' }, error }
    } else {
      // Add reaction
      const { data, error } = await supabase
        .from('blog_reactions')
        .insert({
          post_id: postId,
          anonymous_id: anonymousId,
          reaction_type: reactionType,
          ip_address: await getClientIP()
        })
        .select()
        .single()
      return { data: { action: 'added', ...data }, error }
    }
  }
}

export const getPostReactions = async (postId: string) => {
  const { data, error } = await supabase
    .from('blog_reactions')
    .select('reaction_type, user_id')
    .eq('post_id', postId)

  return { data, error }
}

export const getUserReaction = async (postId: string, userId?: string) => {
  if (userId) {
    // Check authenticated user reaction
    const { data, error } = await supabase
      .from('blog_reactions')
      .select('reaction_type')
      .eq('post_id', postId)
      .eq('user_id', userId)
      .single()
    return { data, error }
  } else {
    // Check anonymous user reaction
    const anonymousId = generateAnonymousId()
    const { data, error } = await supabase
      .from('blog_reactions')
      .select('reaction_type')
      .eq('post_id', postId)
      .eq('anonymous_id', anonymousId)
      .single()
    return { data, error }
  }
}

// Helper functions for anonymous users
const generateAnonymousId = (): string => {
  // Generate a consistent ID based on browser fingerprint
  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')
  ctx!.textBaseline = 'top'
  ctx!.font = '14px Arial'
  ctx!.fillText('Anonymous user fingerprint', 2, 2)

  const fingerprint = canvas.toDataURL() +
    navigator.userAgent +
    navigator.language +
    screen.width +
    screen.height +
    new Date().getTimezoneOffset()

  // Simple hash function
  let hash = 0
  for (let i = 0; i < fingerprint.length; i++) {
    const char = fingerprint.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // Convert to 32-bit integer
  }
  return 'anon_' + Math.abs(hash).toString(36)
}

const getClientIP = async (): Promise<string | null> => {
  try {
    // In production, you might want to use a service to get the real IP
    // For now, we'll use a placeholder
    return '127.0.0.1'
  } catch {
    return null
  }
}

// Comments
export const getPostComments = async (postId: string) => {
  const { data, error } = await supabase
    .from('blog_comments')
    .select(`
      *,
      user:profiles(id, full_name, email, avatar_url)
    `)
    .eq('post_id', postId)
    .eq('is_verified', true)
    .order('created_at', { ascending: true })

  return { data, error }
}

// Send signup reminder email
const sendSignupReminder = async (
  email: string,
  name: string,
  commentContent: string,
  wantsPromotions: boolean = false
) => {
  try {
    // Store the notification in the database
    await supabase
      .from('guest_comment_notifications')
      .insert({
        email,
        verification_token: crypto.randomUUID()
      })

    // In a real implementation, you would send an email here
    // For now, we'll just log it
    console.log(`Signup reminder should be sent to ${email} for user ${name}`)

    // You could integrate with services like:
    // - Resend
    // - SendGrid
    // - Mailgun
    // - AWS SES

    return true
  } catch (error) {
    console.error('Error sending signup reminder:', error)
    return false
  }
}

// Feature Request System Functions

// Boards
export const getFeatureRequestBoards = async () => {
  const { data, error } = await supabase
    .from('feature_request_boards')
    .select('*')
    .eq('is_active', true)
    .order('sort_order', { ascending: true })

  return { data, error }
}

// Statuses
export const getFeatureRequestStatuses = async () => {
  const { data, error } = await supabase
    .from('feature_request_statuses')
    .select('*')
    .eq('is_active', true)
    .order('sort_order', { ascending: true })

  return { data, error }
}

// Feature Requests
export const getFeatureRequests = async (boardId?: string, statusId?: string) => {
  let query = supabase
    .from('feature_requests')
    .select(`
      *,
      board:feature_request_boards(id, name, slug, color, icon),
      status:feature_request_statuses(id, name, slug, color, icon),
      author:profiles(id, full_name, email, avatar_url)
    `)
    .eq('is_public', true)
    .eq('is_archived', false)
    .order('upvotes_count', { ascending: false })

  if (boardId) {
    query = query.eq('board_id', boardId)
  }

  if (statusId) {
    query = query.eq('status_id', statusId)
  }

  const { data, error } = await query
  return { data, error }
}

export const getFeatureRequest = async (id: string) => {
  const { data, error } = await supabase
    .from('feature_requests')
    .select(`
      *,
      board:feature_request_boards(id, name, slug, color, icon),
      status:feature_request_statuses(id, name, slug, color, icon),
      author:profiles(id, full_name, email, avatar_url)
    `)
    .eq('id', id)
    .single()

  return { data, error }
}

export const createFeatureRequest = async (request: {
  board_id: string
  title: string
  description: string
  author_email?: string
  author_name?: string
  priority?: string
}) => {
  const { data: { user } } = await supabase.auth.getUser()

  const requestData = {
    ...request,
    author_id: user?.id || null,
    author_email: user?.email || request.author_email,
    author_name: user?.user_metadata?.full_name || request.author_name
  }

  const { data, error } = await supabase
    .from('feature_requests')
    .insert(requestData)
    .select(`
      *,
      board:feature_request_boards(id, name, slug, color, icon),
      status:feature_request_statuses(id, name, slug, color, icon),
      author:profiles(id, full_name, email, avatar_url)
    `)
    .single()

  return { data, error }
}

export const updateFeatureRequest = async (id: string, updates: any) => {
  const { data, error } = await supabase
    .from('feature_requests')
    .update({ ...updates, updated_at: new Date().toISOString() })
    .eq('id', id)
    .select(`
      *,
      board:feature_request_boards(id, name, slug, color, icon),
      status:feature_request_statuses(id, name, slug, color, icon),
      author:profiles(id, full_name, email, avatar_url)
    `)
    .single()

  return { data, error }
}

// Upvotes
export const toggleFeatureRequestUpvote = async (requestId: string, voterEmail?: string, voterName?: string) => {
  const { data: { user } } = await supabase.auth.getUser()

  if (user) {
    // Authenticated user vote
    const { data: existing } = await supabase
      .from('feature_request_upvotes')
      .select('id')
      .eq('feature_request_id', requestId)
      .eq('user_id', user.id)
      .single()

    if (existing) {
      // Remove vote
      const { error } = await supabase
        .from('feature_request_upvotes')
        .delete()
        .eq('id', existing.id)
      return { data: { action: 'removed' }, error }
    } else {
      // Add vote
      const { data, error } = await supabase
        .from('feature_request_upvotes')
        .insert({
          feature_request_id: requestId,
          user_id: user.id
        })
        .select()
        .single()
      return { data: { action: 'added', ...data }, error }
    }
  } else if (voterEmail) {
    // Guest vote
    const { data: existing } = await supabase
      .from('feature_request_upvotes')
      .select('id')
      .eq('feature_request_id', requestId)
      .eq('voter_email', voterEmail)
      .single()

    if (existing) {
      // Remove vote
      const { error } = await supabase
        .from('feature_request_upvotes')
        .delete()
        .eq('id', existing.id)
      return { data: { action: 'removed' }, error }
    } else {
      // Add vote
      const { data, error } = await supabase
        .from('feature_request_upvotes')
        .insert({
          feature_request_id: requestId,
          voter_email: voterEmail,
          voter_name: voterName
        })
        .select()
        .single()
      return { data: { action: 'added', ...data }, error }
    }
  } else {
    return { error: 'Authentication or email required for voting' }
  }
}

export const getUserUpvote = async (requestId: string, userEmail?: string) => {
  const { data: { user } } = await supabase.auth.getUser()

  if (user) {
    const { data, error } = await supabase
      .from('feature_request_upvotes')
      .select('id')
      .eq('feature_request_id', requestId)
      .eq('user_id', user.id)
      .single()
    return { data: !!data, error }
  } else if (userEmail) {
    const { data, error } = await supabase
      .from('feature_request_upvotes')
      .select('id')
      .eq('feature_request_id', requestId)
      .eq('voter_email', userEmail)
      .single()
    return { data: !!data, error }
  }

  return { data: false, error: null }
}

export const createComment = async (
  postId: string,
  content: string,
  parentId?: string,
  guestInfo?: {
    email: string;
    name: string;
    isAnonymous?: boolean;
    wantsNotifications?: boolean;
    wantsPromotions?: boolean;
  }
) => {
  // Import security functions
  const { sanitizeHtml, validateInput } = await import('./security')

  // Sanitize content to prevent XSS
  const sanitizedContent = sanitizeHtml(content)

  if (!sanitizedContent.trim()) {
    return { error: 'Comment content is required' }
  }

  const { data: { user } } = await supabase.auth.getUser()

  if (user) {
    // Authenticated user comment
    const { data, error } = await supabase
      .from('blog_comments')
      .insert({
        post_id: postId,
        user_id: user.id,
        content: sanitizedContent,
        parent_id: parentId || null,
        is_verified: true
      })
      .select(`
        *,
        user:profiles(id, full_name, email, avatar_url)
      `)
      .single()

    return { data, error }
  } else if (guestInfo) {
    // Validate guest input
    if (!validateInput.email(guestInfo.email)) {
      return { error: 'Invalid email address' }
    }

    if (!guestInfo.isAnonymous && !validateInput.name(guestInfo.name)) {
      return { error: 'Invalid name' }
    }
    // Guest user comment
    const verificationToken = crypto.randomUUID()

    const { data, error } = await supabase
      .from('blog_comments')
      .insert({
        post_id: postId,
        content: sanitizedContent,
        parent_id: parentId || null,
        guest_email: guestInfo.email, // Always required now
        guest_name: guestInfo.isAnonymous ? 'Anonymous' : guestInfo.name,
        is_anonymous: guestInfo.isAnonymous || false,
        verification_token: verificationToken,
        is_verified: true, // Auto-verify for now, can add email verification later
        wants_notifications: guestInfo.wantsNotifications || false,
        wants_promotions: guestInfo.wantsPromotions || false
      })
      .select()
      .single()

    // Send signup reminder email (always send since email is required)
    if (guestInfo.email) {
      await sendSignupReminder(
        guestInfo.email,
        guestInfo.isAnonymous ? 'Anonymous' : guestInfo.name,
        sanitizedContent,
        guestInfo.wantsPromotions || false
      )
    }

    return { data, error }
  } else {
    return { error: 'Authentication or guest information required' }
  }
}

export const deleteComment = async (commentId: string) => {
  const { error } = await supabase
    .from('blog_comments')
    .delete()
    .eq('id', commentId)

  return { error }
}

// Email Integration Functions
export const getEmailIntegrations = async () => {
  const { data, error } = await supabase
    .from('email_integrations')
    .select('*')
    .order('created_at', { ascending: false })

  return { data, error }
}

export const createEmailIntegration = async (integration: Omit<EmailIntegration, 'id' | 'user_id' | 'created_at' | 'updated_at'>) => {
  const { data: { user } } = await supabase.auth.getUser()

  if (!user) {
    return { data: null, error: { message: 'User not authenticated' } }
  }

  // If this is set as default, unset other defaults
  if (integration.is_default) {
    await supabase
      .from('email_integrations')
      .update({ is_default: false })
      .eq('user_id', user.id)
  }

  const { data, error } = await supabase
    .from('email_integrations')
    .insert({
      ...integration,
      user_id: user.id
    })
    .select()
    .single()

  return { data, error }
}

export const updateEmailIntegration = async (id: string, updates: Partial<EmailIntegration>) => {
  const { data: { user } } = await supabase.auth.getUser()

  if (!user) {
    return { data: null, error: { message: 'User not authenticated' } }
  }

  // If this is set as default, unset other defaults
  if (updates.is_default) {
    await supabase
      .from('email_integrations')
      .update({ is_default: false })
      .eq('user_id', user.id)
      .neq('id', id)
  }

  const { data, error } = await supabase
    .from('email_integrations')
    .update(updates)
    .eq('id', id)
    .eq('user_id', user.id)
    .select()
    .single()

  return { data, error }
}

export const deleteEmailIntegration = async (id: string) => {
  const { data: { user } } = await supabase.auth.getUser()

  if (!user) {
    return { error: { message: 'User not authenticated' } }
  }

  const { error } = await supabase
    .from('email_integrations')
    .delete()
    .eq('id', id)
    .eq('user_id', user.id)

  return { error }
}

export const testEmailIntegration = async (integrationId: string, testEmail: string) => {
  try {
    // Get the integration
    const { data: integration, error } = await supabase
      .from('email_integrations')
      .select('*')
      .eq('id', integrationId)
      .single()

    if (error || !integration) {
      return { success: false, message: 'Integration not found' }
    }

    // Import email service dynamically to avoid circular imports
    const { createEmailService } = await import('./emailService')

    // Create email service and test
    const emailService = createEmailService(integration)
    const result = await emailService.testIntegration(testEmail)

    return {
      success: result.success,
      message: result.success
        ? 'Test email sent successfully!'
        : result.error || 'Failed to send test email'
    }
  } catch (error) {
    console.error('Error testing email integration:', error)
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    }
  }
}

// Email Template Functions
export const getEmailTemplates = async (integrationId?: string) => {
  let query = supabase
    .from('email_templates')
    .select(`
      *,
      integration:email_integrations(id, name, provider)
    `)
    .order('created_at', { ascending: false })

  if (integrationId) {
    query = query.eq('integration_id', integrationId)
  }

  const { data, error } = await query
  return { data, error }
}

export const createEmailTemplate = async (template: Omit<EmailTemplate, 'id' | 'user_id' | 'created_at' | 'updated_at'>) => {
  const { data: { user } } = await supabase.auth.getUser()

  if (!user) {
    return { data: null, error: { message: 'User not authenticated' } }
  }

  // If this is set as default, unset other defaults for this integration
  if (template.is_default) {
    await supabase
      .from('email_templates')
      .update({ is_default: false })
      .eq('user_id', user.id)
      .eq('integration_id', template.integration_id)
  }

  const { data, error } = await supabase
    .from('email_templates')
    .insert({
      ...template,
      user_id: user.id
    })
    .select()
    .single()

  return { data, error }
}

export const updateEmailTemplate = async (id: string, updates: Partial<EmailTemplate>) => {
  const { data: { user } } = await supabase.auth.getUser()

  if (!user) {
    return { data: null, error: { message: 'User not authenticated' } }
  }

  // If this is set as default, unset other defaults for this integration
  if (updates.is_default && updates.integration_id) {
    await supabase
      .from('email_templates')
      .update({ is_default: false })
      .eq('user_id', user.id)
      .eq('integration_id', updates.integration_id)
      .neq('id', id)
  }

  const { data, error } = await supabase
    .from('email_templates')
    .update(updates)
    .eq('id', id)
    .eq('user_id', user.id)
    .select()
    .single()

  return { data, error }
}

export const deleteEmailTemplate = async (id: string) => {
  const { data: { user } } = await supabase.auth.getUser()

  if (!user) {
    return { error: { message: 'User not authenticated' } }
  }

  const { error } = await supabase
    .from('email_templates')
    .delete()
    .eq('id', id)
    .eq('user_id', user.id)

  return { error }
}

// Saves/Bookmarks
export const toggleSave = async (postId: string) => {
  const { data: { user } } = await supabase.auth.getUser()
  if (!user) return { error: 'Not authenticated' }

  // Check if save already exists
  const { data: existing } = await supabase
    .from('blog_saves')
    .select('id')
    .eq('post_id', postId)
    .eq('user_id', user.id)
    .single()

  if (existing) {
    // Remove save
    const { error } = await supabase
      .from('blog_saves')
      .delete()
      .eq('id', existing.id)
    return { data: { action: 'removed' }, error }
  } else {
    // Add save
    const { data, error } = await supabase
      .from('blog_saves')
      .insert({ post_id: postId, user_id: user.id })
      .select()
      .single()
    return { data: { action: 'added', ...data }, error }
  }
}

export const getUserSaves = async () => {
  const { data: { user } } = await supabase.auth.getUser()
  if (!user) return { error: 'Not authenticated' }

  const { data, error } = await supabase
    .from('blog_saves')
    .select(`
      *,
      post:blog_posts(
        *,
        author:profiles!author_id(id, full_name, email, avatar_url)
      )
    `)
    .eq('user_id', user.id)
    .order('created_at', { ascending: false })

  return { data, error }
}

export const isPostSaved = async (postId: string, userId: string) => {
  const { data, error } = await supabase
    .from('blog_saves')
    .select('id')
    .eq('post_id', postId)
    .eq('user_id', userId)
    .single()

  return { data: !!data, error }
}

// Shares
export const recordShare = async (postId: string, shareType: string) => {
  const { data: { user } } = await supabase.auth.getUser()

  const { data, error } = await supabase
    .from('blog_shares')
    .insert({
      post_id: postId,
      user_id: user?.id || null,
      share_type: shareType
    })
    .select()
    .single()

  return { data, error }
}

// ============================================================================
// TESTIMONIALS & CASE STUDIES
// ============================================================================

export const createTestimonial = async (testimonialData: {
  author_name: string
  author_title?: string
  author_company?: string
  author_industry?: string
  quote: string
  rating: number
  project_type?: string
  project_value?: number
  timeline_months?: number
  results_achieved?: string
}) => {
  const { data: { user } } = await supabase.auth.getUser()

  if (!user) {
    return { error: 'Authentication required' }
  }

  // Import security functions
  const { sanitizeHtml, validateInput } = await import('./security')

  // Validate and sanitize input
  if (!validateInput.name(testimonialData.author_name)) {
    return { error: 'Invalid author name' }
  }

  const sanitizedQuote = sanitizeHtml(testimonialData.quote)
  if (!sanitizedQuote.trim()) {
    return { error: 'Quote is required' }
  }

  const { data, error } = await supabase
    .from('testimonials')
    .insert({
      ...testimonialData,
      quote: sanitizedQuote,
      author_id: user.id,
      is_approved: false, // Requires admin approval
      is_featured: false,
      is_generated: false
    })
    .select()
    .single()

  return { data, error }
}

export const createCaseStudy = async (caseStudyData: {
  title: string
  client_name?: string
  client_industry?: string
  project_type?: string
  challenge: string
  solution: string
  results: string
  technologies?: string[]
  timeline_months?: number
  team_size?: number
  project_value?: number
  roi_percentage?: number
  featured_image?: string
}) => {
  const { data: { user } } = await supabase.auth.getUser()

  if (!user) {
    return { error: 'Authentication required' }
  }

  // Import security functions
  const { sanitizeHtml, validateInput } = await import('./security')

  // Validate and sanitize input
  if (!validateInput.name(caseStudyData.title)) {
    return { error: 'Invalid title' }
  }

  const sanitizedChallenge = sanitizeHtml(caseStudyData.challenge)
  const sanitizedSolution = sanitizeHtml(caseStudyData.solution)
  const sanitizedResults = sanitizeHtml(caseStudyData.results)

  if (!sanitizedChallenge.trim() || !sanitizedSolution.trim() || !sanitizedResults.trim()) {
    return { error: 'Challenge, solution, and results are required' }
  }

  // Generate slug from title
  const slug = caseStudyData.title
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)/g, '')

  const { data, error } = await supabase
    .from('case_studies')
    .insert({
      ...caseStudyData,
      slug,
      challenge: sanitizedChallenge,
      solution: sanitizedSolution,
      results: sanitizedResults,
      author_id: user.id,
      is_approved: false, // Requires admin approval
      is_featured: false,
      is_generated: false
    })
    .select()
    .single()

  return { data, error }
}

export const getTestimonialsFromTable = async (filters?: {
  project_type?: string
  is_featured?: boolean
  limit?: number
}) => {
  let query = supabase
    .from('testimonials')
    .select('*')
    .eq('is_approved', true)
    .order('is_featured', { ascending: false })
    .order('display_order', { ascending: true })

  if (filters?.project_type) {
    query = query.eq('project_type', filters.project_type)
  }

  if (filters?.is_featured !== undefined) {
    query = query.eq('is_featured', filters.is_featured)
  }

  if (filters?.limit) {
    query = query.limit(filters.limit)
  }

  return await query
}

export const getCaseStudies = async (filters?: {
  project_type?: string
  is_featured?: boolean
  limit?: number
}) => {
  let query = supabase
    .from('case_studies')
    .select('*')
    .eq('is_approved', true)
    .order('is_featured', { ascending: false })
    .order('display_order', { ascending: true })

  if (filters?.project_type) {
    query = query.eq('project_type', filters.project_type)
  }

  if (filters?.is_featured !== undefined) {
    query = query.eq('is_featured', filters.is_featured)
  }

  if (filters?.limit) {
    query = query.limit(filters.limit)
  }

  return await query
}

export const getCaseStudyBySlug = async (slug: string) => {
  return await supabase
    .from('case_studies')
    .select(`
      *,
      case_study_metrics(*)
    `)
    .eq('slug', slug)
    .eq('is_approved', true)
    .single()
}

// Newsletter subscription functions
export const subscribeToNewsletter = async (email: string, name?: string, source: string = 'website') => {
  try {
    const response = await fetch(`${supabaseUrl}/functions/v1/newsletter-subscribe`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${supabaseAnonKey}`,
      },
      body: JSON.stringify({
        email: email.toLowerCase().trim(),
        name,
        source
      })
    })

    const result = await response.json()
    return { data: result, error: result.success ? null : new Error(result.error) }
  } catch (error) {
    return { data: null, error }
  }
}

export const getNewsletterSubscribers = async () => {
  const { data, error } = await supabase
    .from('newsletter_subscribers')
    .select('*')
    .order('created_at', { ascending: false })

  return { data, error }
}

export const updateNewsletterSubscriber = async (id: string, updates: Partial<NewsletterSubscriber>) => {
  const { data, error } = await supabase
    .from('newsletter_subscribers')
    .update(updates)
    .eq('id', id)
    .select()
    .single()

  return { data, error }
}

export const unsubscribeFromNewsletter = async (email: string) => {
  const { data, error } = await supabase
    .from('newsletter_subscribers')
    .update({
      status: 'unsubscribed',
      unsubscribed_at: new Date().toISOString()
    })
    .eq('email', email.toLowerCase())
    .select()
    .single()

  return { data, error }
}
