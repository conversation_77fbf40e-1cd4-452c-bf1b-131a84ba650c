import React, { useEffect, useState } from 'react'
import { usePara<PERSON>, useNavigate, Link } from 'react-router-dom'
import { useAuth } from '@/contexts/AuthContext'
import ProtectedRoute from '@/components/auth/ProtectedRoute'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { getBlogPostById, createBlogPost, updateBlogPost, BlogPost, uploadFile, createOrUpdateProfile } from '@/lib/supabase'
import { useOrganization } from '@/contexts/OrganizationContext'
import { ArrowLeft, Save, Eye, Loader2, Upload, Image, Video, FileText, ChevronDown, Building2, Globe } from 'lucide-react'
import { toast } from 'sonner'
import RichTextEditor from '@/components/RichTextEditor'

const BlogEditor = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const { user, profile } = useAuth()
  const { currentOrganization } = useOrganization()
  const isEditing = Boolean(id)

  const [loading, setLoading] = useState(isEditing)
  const [saving, setSaving] = useState(false)
  const [uploading, setUploading] = useState(false)
  const [uploadedImages, setUploadedImages] = useState<Array<{url: string, name: string}>>([])
  const [post, setPost] = useState<Partial<BlogPost>>({
    title: '',
    slug: '',
    excerpt: '',
    content: '',
    status: 'draft',
    author_id: user?.id || '',
    author_display: 'real_name',
    custom_name: '',
    thumbnail_url: '',
    category: 'blog', // Default to blog
    tags: [], // Default to empty array
    is_featured: false, // Default to not featured
    display_order: 0 // Default display order
  })

  useEffect(() => {
    if (isEditing && id) {
      fetchPost(id)
    }
  }, [id, isEditing])

  const fetchPost = async (postId: string) => {
    try {
      const { data, error } = await getBlogPostById(postId)
      if (error || !data) {
        toast.error('Failed to load blog post')
        navigate('/admin/blog')
        return
      }

      // Security check: Only allow users to edit their own posts (unless admin+)
      const canEdit = data.author_id === user?.id ||
                     (profile?.role && ['admin', 'super_admin', 'owner', 'saas_owner'].includes(profile.role))

      if (!canEdit) {
        toast.error('You can only edit your own posts')
        navigate('/admin/blog')
        return
      }

      // Convert content for rich text editor
      const editorContent = extractContentForEditor(data.content)
      console.log('Loaded post data:', data)
      console.log('Author display from DB:', data.author_display)
      setPost({
        ...data,
        content: editorContent,
        author_display: data.author_display || 'real_name' // Fallback for existing posts
      })
    } catch (error) {
      toast.error('An unexpected error occurred')
      navigate('/admin/blog')
    } finally {
      setLoading(false)
    }
  }

  const extractContentForEditor = (content: any): string => {
    console.log('Extracting content for editor:', content, typeof content)

    // If it's already HTML string, return as is for the rich text editor
    if (typeof content === 'string') {
      // Check if it looks like HTML
      if (content.includes('<') && content.includes('>')) {
        console.log('Content is HTML, returning as-is')
        return content
      }
      // Otherwise, wrap plain text in paragraph
      console.log('Content is plain text, wrapping in paragraph')
      return `<p>${content}</p>`
    }

    // If it's Tiptap JSON, convert to HTML (legacy support)
    if (content && content.content) {
      console.log('Content is Tiptap JSON, converting to HTML')
      // For now, extract text and wrap in paragraphs
      let text = ''
      const traverse = (nodes: any[]) => {
        nodes.forEach(node => {
          if (node.type === 'text') {
            text += node.text
          } else if (node.content) {
            traverse(node.content)
          }
          if (node.type === 'paragraph' && text && !text.endsWith('\n')) {
            text += '\n'
          }
        })
      }
      traverse(content.content)
      return text.trim() ? `<p>${text.trim().replace(/\n/g, '</p><p>')}</p>` : ''
    }

    console.log('Content format not recognized, returning empty')
    return ''
  }

  const generateSlug = (title: string) => {
    const baseSlug = title
      .toLowerCase()
      .replace(/[^a-z0-9 -]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .replace(/(^-|-$)/g, '')
      .trim()

    // Add timestamp suffix to ensure uniqueness
    const timestamp = Date.now()
    return `${baseSlug}-${timestamp}`
  }

  const handleTitleChange = (title: string) => {
    setPost(prev => ({
      ...prev,
      title,
      slug: generateSlug(title)
    }))
  }

  const handleSave = async (status: 'draft' | 'pending_approval' | 'published', destination?: 'workspace' | 'mbi') => {
    if (!post.title?.trim()) {
      toast.error('Please enter a title')
      return
    }

    if (!post.content?.trim()) {
      toast.error('Please enter some content')
      return
    }

    if (!user?.id) {
      toast.error('User not authenticated')
      return
    }

    setSaving(true)
    try {
      // Ensure user profile exists
      await createOrUpdateProfile(user)

      // Store HTML content directly - the rich text editor provides HTML
      const contentData = post.content
      console.log('Saving content:', contentData)

      // Determine submission status based on destination
      let submissionStatus = 'private' // Default to workspace
      if (destination === 'mbi') {
        submissionStatus = 'submitted' // Submit to MBI for approval
      }

      // Clean the post data - only include fields that exist in the database
      const postData: any = {
        title: post.title.trim(),
        slug: post.slug?.trim() || generateSlug(post.title),
        content: contentData,
        excerpt: post.excerpt?.trim() || null,
        featured_image: post.featured_image || null,
        thumbnail_url: post.thumbnail_url || null,
        status,
        author_id: user.id,
        author_display: post.author_display || 'real_name',
        category: post.category || 'blog',
        tags: post.tags || [],
        is_featured: post.is_featured || false,
        display_order: post.display_order || 0,
        organization_id: currentOrganization?.id,
        submission_status: submissionStatus
      }

      // Only add published_at for published posts
      if (status === 'published') {
        postData.published_at = new Date().toISOString()
      }

      // Check if user can publish directly or needs approval
      const canPublishDirectly = profile?.role && ['admin', 'super_admin', 'owner', 'saas_owner'].includes(profile.role)

      // If regular user tries to publish, change to pending_approval
      if (status === 'published' && !canPublishDirectly) {
        postData.status = 'pending_approval'
        delete postData.published_at // Remove published_at for pending posts
      }

      console.log('Saving post data:', postData)
      console.log('Is editing:', isEditing, 'Post ID:', id)

      let result
      if (isEditing && id) {
        console.log('Updating existing post with ID:', id)
        result = await updateBlogPost(id, postData)
      } else {
        console.log('Creating new post')
        result = await createBlogPost(postData)
      }

      console.log('Save result:', result)

      if (result.error) {
        toast.error('Failed to save blog post')
        console.error('Save error:', result.error)
        console.error('Post data:', postData)
        return
      }

      // Show appropriate success message
      let successMessage = 'Blog post saved successfully!'
      if (destination === 'mbi') {
        successMessage = 'Blog post submitted to MBI for review!'
      } else if (status === 'published' && canPublishDirectly) {
        successMessage = 'Blog post published to workspace!'
      } else if (status === 'published' && !canPublishDirectly) {
        successMessage = 'Blog post submitted for approval!'
      } else if (postData.status === 'pending_approval') {
        successMessage = 'Blog post submitted for approval!'
      }

      toast.success(successMessage)
      navigate('/admin/blog')
    } catch (error) {
      toast.error('An unexpected error occurred')
      console.error('Save error:', error)
    } finally {
      setSaving(false)
    }
  }

  const handlePreview = () => {
    if (post.slug) {
      window.open(`/blog/${post.slug}`, '_blank')
    } else {
      toast.error('Please save the post first to preview')
    }
  }

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Check file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'video/mp4', 'video/webm']
    if (!allowedTypes.includes(file.type)) {
      toast.error('Please upload an image (JPEG, PNG, GIF, WebP) or video (MP4, WebM)')
      return
    }

    // Check file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      toast.error('File size must be less than 10MB')
      return
    }

    setUploading(true)
    try {
      const { data, error } = await uploadFile(file, 'blog-images')
      if (error) {
        toast.error('Failed to upload file')
        console.error('Upload error:', error)
        return
      }

      if (data?.publicUrl) {
        // Insert the media into the rich text editor
        if (file.type.startsWith('image/')) {
          // Add to uploaded images list
          setUploadedImages(prev => [...prev, { url: data.publicUrl, name: file.name }])

          // For images, insert as HTML img tag
          const imageHtml = `<img src="${data.publicUrl}" alt="${file.name}" class="max-w-full h-auto rounded-lg" />`
          const currentContent = post.content || ''
          const newContent = currentContent + imageHtml
          setPost(prev => ({ ...prev, content: newContent }))

          // Ask if user wants to set this as thumbnail
          const setAsThumbnail = window.confirm('Would you like to set this image as the post thumbnail?')
          if (setAsThumbnail) {
            setPost(prev => ({ ...prev, thumbnail_url: data.publicUrl }))
          }
        } else {
          // For videos and other files, insert as link
          const linkHtml = `<a href="${data.publicUrl}" target="_blank" rel="noopener noreferrer">${file.name}</a>`
          const currentContent = post.content || ''
          const newContent = currentContent + linkHtml
          setPost(prev => ({ ...prev, content: newContent }))
        }

        toast.success('File uploaded successfully!')
      }
    } catch (error) {
      toast.error('An unexpected error occurred')
      console.error('Upload error:', error)
    } finally {
      setUploading(false)
      // Reset the input
      event.target.value = ''
    }
  }

  if (loading) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-background flex items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 sm:px-6 py-4 sm:py-8">
          {/* Header */}
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 mb-8">
            <div className="flex items-center gap-4">
              <Link to="/admin/blog">
                <Button variant="ghost" size="sm">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Dashboard
                </Button>
              </Link>
              <h1 className="text-xl sm:text-2xl font-bold">
                {isEditing ? 'Edit Post' : 'Create New Post'}
              </h1>
            </div>

            <div className="flex flex-wrap items-center gap-2 w-full sm:w-auto">
              <Button
                variant="outline"
                onClick={handlePreview}
                disabled={!post.slug}
                className="flex-1 sm:flex-none"
              >
                <Eye className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Preview</span>
              </Button>
              <Button
                variant="outline"
                onClick={() => handleSave('draft')}
                disabled={saving}
                className="flex-1 sm:flex-none"
              >
                {saving ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <Save className="h-4 w-4 mr-2" />}
                <span className="hidden sm:inline">Save Draft</span>
                <span className="sm:hidden">Draft</span>
              </Button>

              {/* Publish Dropdown */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    disabled={saving}
                    className="bg-gradient-to-r from-primary to-accent text-white flex-1 sm:flex-none"
                  >
                    {saving ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : null}
                    Publish
                    <ChevronDown className="h-4 w-4 ml-2" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuItem
                    onClick={() => handleSave('published', 'workspace')}
                    className="flex items-center gap-2 cursor-pointer"
                  >
                    <Building2 className="h-4 w-4 text-blue-500" />
                    <div className="flex flex-col">
                      <span className="font-medium">Publish to Workspace</span>
                      <span className="text-xs text-muted-foreground">
                        Private to your organization
                      </span>
                    </div>
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => handleSave('published', 'mbi')}
                    className="flex items-center gap-2 cursor-pointer"
                  >
                    <Globe className="h-4 w-4 text-green-500" />
                    <div className="flex flex-col">
                      <span className="font-medium">Submit to MBI Blog</span>
                      <span className="text-xs text-muted-foreground">
                        Public after review & approval
                      </span>
                    </div>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {/* Editor */}
          <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
            {/* Main Content */}
            <div className="xl:col-span-2 space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Content</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4 p-4 sm:p-6">
                  <div>
                    <Label htmlFor="title">Title</Label>
                    <Input
                      id="title"
                      value={post.title || ''}
                      onChange={(e) => handleTitleChange(e.target.value)}
                      placeholder="Enter post title..."
                      className="text-base sm:text-lg font-semibold"
                    />
                  </div>

                  <div>
                    <Label htmlFor="slug">URL Slug</Label>
                    <Input
                      id="slug"
                      value={post.slug || ''}
                      onChange={(e) => setPost(prev => ({ ...prev, slug: e.target.value }))}
                      placeholder="url-slug"
                    />
                  </div>

                  <div>
                    <Label htmlFor="excerpt">Excerpt</Label>
                    <Textarea
                      id="excerpt"
                      value={post.excerpt || ''}
                      onChange={(e) => setPost(prev => ({ ...prev, excerpt: e.target.value }))}
                      placeholder="Brief description of the post..."
                      rows={3}
                      className="resize-none"
                    />
                  </div>

                  <div>
                    <Label htmlFor="thumbnail">Thumbnail</Label>
                    <Input
                      id="thumbnail"
                      value={post.thumbnail_url || ''}
                      onChange={(e) => setPost(prev => ({ ...prev, thumbnail_url: e.target.value }))}
                      placeholder={uploadedImages.length > 0 ? "Enter URL or click an image below" : "Enter thumbnail URL or upload images first"}
                    />

                    {/* Instructions */}
                    {uploadedImages.length === 0 && (
                      <p className="text-sm text-gray-500 mt-1">
                        💡 Upload images using the "Upload Media" button above, then select one as your thumbnail
                      </p>
                    )}

                    {/* Uploaded Images for Thumbnail Selection */}
                    {uploadedImages.length > 0 && (
                      <div className="mt-3">
                        <Label className="text-sm text-gray-600 font-medium">
                          📸 Click an image to set as thumbnail:
                        </Label>
                        <div className="grid grid-cols-4 gap-2 mt-2">
                          {uploadedImages.map((image, index) => (
                            <div
                              key={index}
                              className={`relative cursor-pointer border-2 rounded overflow-hidden transition-all hover:scale-105 ${
                                post.thumbnail_url === image.url ? 'border-primary ring-2 ring-primary/20' : 'border-gray-200 hover:border-gray-300'
                              }`}
                              onClick={() => setPost(prev => ({ ...prev, thumbnail_url: image.url }))}
                              title={`Click to set "${image.name}" as thumbnail`}
                            >
                              <img
                                src={image.url}
                                alt={image.name}
                                className="w-full h-16 object-cover"
                              />
                              {post.thumbnail_url === image.url && (
                                <div className="absolute inset-0 bg-primary/20 flex items-center justify-center">
                                  <div className="w-4 h-4 bg-primary rounded-full border-2 border-white"></div>
                                </div>
                              )}
                              <div className="absolute bottom-0 left-0 right-0 bg-black/50 text-white text-xs p-1 truncate">
                                {image.name}
                              </div>
                            </div>
                          ))}
                        </div>
                        <p className="text-xs text-gray-500 mt-1">
                          Selected thumbnail will appear in blog listings and social media previews
                        </p>
                      </div>
                    )}

                    {/* Current Thumbnail Preview */}
                    {post.thumbnail_url && (
                      <div className="mt-3">
                        <Label className="text-sm text-gray-600 font-medium">✅ Current thumbnail:</Label>
                        <div className="mt-1 relative inline-block">
                          <img
                            src={post.thumbnail_url}
                            alt="Thumbnail preview"
                            className="w-32 h-20 object-cover rounded border-2 border-primary"
                          />
                          <button
                            type="button"
                            onClick={() => setPost(prev => ({ ...prev, thumbnail_url: '' }))}
                            className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full text-xs hover:bg-red-600 transition-colors"
                            title="Remove thumbnail"
                          >
                            ×
                          </button>
                        </div>
                      </div>
                    )}
                  </div>

                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <Label htmlFor="content">Content</Label>
                      <div className="flex items-center gap-2">
                        <input
                          type="file"
                          id="file-upload"
                          accept="image/*,video/*,.gif"
                          onChange={handleFileUpload}
                          className="hidden"
                        />
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => document.getElementById('file-upload')?.click()}
                          disabled={uploading}
                        >
                          {uploading ? (
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          ) : (
                            <Upload className="h-4 w-4 mr-2" />
                          )}
                          Upload Media
                        </Button>
                      </div>
                    </div>
                    <RichTextEditor
                      content={post.content || ''}
                      onChange={(content) => setPost(prev => ({ ...prev, content }))}
                      placeholder="Write your blog post content here... Use the toolbar above for formatting."
                      className="min-h-[300px] sm:min-h-[400px]"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Tip: Use the toolbar for rich formatting. Upload images/videos using the button above or the image tool in the toolbar.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              <Card>
                <CardHeader className="p-4 sm:p-6">
                  <CardTitle>Post Settings</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4 p-4 sm:p-6">
                  <div>
                    <Label htmlFor="status">Status</Label>
                    <Select
                      value={post.status || 'draft'}
                      onValueChange={(value: 'draft' | 'published') =>
                        setPost(prev => ({ ...prev, status: value }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="draft">Draft</SelectItem>
                        <SelectItem value="published">Published</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="category">Publish Destination</Label>
                    <Select
                      value={post.category || 'blog'}
                      onValueChange={(value: 'blog' | 'testimonials') =>
                        setPost(prev => ({ ...prev, category: value }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="blog">
                          <div className="flex flex-col">
                            <span className="font-medium">Blog Section</span>
                            <span className="text-sm text-gray-500">Regular blog post</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="testimonials">
                          <div className="flex flex-col">
                            <span className="font-medium">Success Stories & Testimonials</span>
                            <span className="text-sm text-gray-500">Testimonials page</span>
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <p className="text-xs text-gray-500 mt-1">
                      {post.category === 'testimonials'
                        ? "This will appear on the Success Stories & Testimonials page"
                        : "This will appear in the main blog feed"
                      }
                    </p>
                  </div>

                  <div>
                    <Label htmlFor="author_display">Author Display</Label>
                    <Select
                      value={post.author_display || 'real_name'}
                      onValueChange={(value: 'real_name' | 'anonymous' | 'mbi_team' | 'custom_name') =>
                        setPost(prev => ({ ...prev, author_display: value }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="real_name">Use Your Name</SelectItem>
                        <SelectItem value="anonymous">Publish Anonymously</SelectItem>
                        <SelectItem value="custom_name">Use Custom Name</SelectItem>
                        <SelectItem value="mbi_team">Publish as MBI Team</SelectItem>
                      </SelectContent>
                    </Select>
                    <p className="text-xs text-gray-500 mt-1">
                      Choose how your name appears on this post
                    </p>
                  </div>

                  {/* Featured Testimonial Settings - Only show for testimonials and admins */}
                  {(post.category === 'testimonials' || (post.tags && post.tags.includes('testimonial'))) &&
                   user && profile && ['owner', 'super_admin', 'admin'].includes(profile.role) && (
                    <div className="border-t pt-4">
                      <h4 className="font-medium text-gray-900 dark:text-white mb-3">Homepage Feature Settings</h4>

                      <div className="space-y-4">
                        <div className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            id="is_featured"
                            checked={post.is_featured || false}
                            onChange={(e) => setPost(prev => ({ ...prev, is_featured: e.target.checked }))}
                            className="rounded border-gray-300 text-primary focus:ring-primary"
                          />
                          <Label htmlFor="is_featured" className="text-sm">
                            Feature on Homepage
                          </Label>
                        </div>

                        {post.is_featured && (
                          <div>
                            <Label htmlFor="display_order">Display Order</Label>
                            <Input
                              type="number"
                              id="display_order"
                              value={post.display_order || 0}
                              onChange={(e) => setPost(prev => ({ ...prev, display_order: parseInt(e.target.value) || 0 }))}
                              placeholder="0"
                              min="0"
                            />
                            <p className="text-xs text-gray-500 mt-1">
                              Lower numbers appear first (0 = highest priority)
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Custom Name Input - Show only when custom_name is selected */}
                  {post.author_display === 'custom_name' && (
                    <div>
                      <Label htmlFor="custom_name">Custom Name</Label>
                      <Input
                        id="custom_name"
                        value={post.custom_name || ''}
                        onChange={(e) => setPost(prev => ({ ...prev, custom_name: e.target.value }))}
                        placeholder="Enter the name to display as author"
                      />
                    </div>
                  )}

                  {post.created_at && (
                    <div>
                      <Label>Created</Label>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {new Date(post.created_at).toLocaleDateString()}
                      </p>
                    </div>
                  )}

                  {post.updated_at && (
                    <div>
                      <Label>Last Updated</Label>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {new Date(post.updated_at).toLocaleDateString()}
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  )
}

export default BlogEditor
