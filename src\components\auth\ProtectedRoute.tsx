import React, { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import AuthModal from './AuthModal'
import { Loader2 } from 'lucide-react'

interface ProtectedRouteProps {
  children: React.ReactNode
  requiredRole?: 'saas_owner' | 'super_admin' | 'admin' | 'editor' | 'viewer'
  fallback?: React.ReactNode
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRole = 'viewer',
  fallback
}) => {
  const { user, profile, loading } = useAuth()
  const [showAuthModal, setShowAuthModal] = useState(false)

  useEffect(() => {
    if (!loading && !user) {
      setShowAuthModal(true)
    } else if (user && profile) {
      setShowAuthModal(false)
    }
  }, [user, profile, loading])

  // Show loading spinner while checking authentication
  if (loading) {
    console.log('ProtectedRoute: Loading state - user:', !!user, 'profile:', !!profile, 'loading:', loading)
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin mx-auto text-primary" />
          <p className="text-gray-600 dark:text-gray-400 font-poppins">
            Checking authentication...
          </p>
        </div>
      </div>
    )
  }

  // Show auth modal if not authenticated
  if (!user) {
    return (
      <>
        {fallback || (
          <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-blue-50 via-cyan-50 to-teal-50 dark:from-gray-900 dark:via-blue-900/10 dark:to-cyan-900/10">
            <div className="text-center space-y-6 p-8">
              <div className="space-y-2">
                <h1 className="text-3xl font-montserrat font-bold text-gray-900 dark:text-white">
                  Start Blogging Today
                </h1>
                <p className="text-gray-600 dark:text-gray-400 font-poppins max-w-md">
                  Sign in to access your blog dashboard and start creating amazing content for free.
                </p>
              </div>
              <button
                onClick={() => setShowAuthModal(true)}
                className="bg-gradient-to-r from-primary to-accent text-white px-6 py-3 rounded-full font-poppins font-semibold hover:shadow-lg transition-all duration-300"
              >
                Sign In to Continue
              </button>
            </div>
          </div>
        )}
        <AuthModal 
          isOpen={showAuthModal} 
          onClose={() => setShowAuthModal(false)} 
        />
      </>
    )
  }

  // Check if user has required role
  if (profile) {
    console.log('ProtectedRoute: Profile check - user role:', profile.role, 'required role:', requiredRole)
    // Fixed role hierarchy: higher numbers = higher permissions
    const roleHierarchy = { viewer: 1, editor: 2, admin: 3, super_admin: 4, saas_owner: 5 }
    const userRoleLevel = roleHierarchy[profile.role] || 0
    const requiredRoleLevel = roleHierarchy[requiredRole] || 0
    console.log('ProtectedRoute: Role levels - user:', userRoleLevel, 'required:', requiredRoleLevel)

    // User must have equal or higher role level than required
    if (userRoleLevel < requiredRoleLevel) {
      return (
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center space-y-4 p-8">
            <h1 className="text-2xl font-montserrat font-bold text-gray-900 dark:text-white">
              Insufficient Permissions
            </h1>
            <p className="text-gray-600 dark:text-gray-400 font-poppins">
              You don't have the required permissions to access this area.
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-500">
              Your role: <span className="font-semibold capitalize">{profile.role}</span> |
              Required: <span className="font-semibold capitalize">{requiredRole}</span>
            </p>
          </div>
        </div>
      )
    }
  } else if (user && !profile) {
    console.log('ProtectedRoute: User exists but no profile - user:', user.email)
    // User exists but profile is not loaded yet, keep showing loading
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin mx-auto text-primary" />
          <p className="text-gray-600 dark:text-gray-400 font-poppins">
            Loading profile...
          </p>
        </div>
      </div>
    )
  }

  // User is authenticated and has required permissions
  return <>{children}</>
}

export default ProtectedRoute
