-- Update SaaS Owner Role
-- Run this in Supabase Dashboard > SQL Editor
-- This will create the SaaS owner role and assign it to <PERSON>

-- 1. First, drop any existing role constraint
ALTER TABLE profiles DROP CONSTRAINT IF EXISTS profiles_role_check;

-- 2. Add the new constraint with all roles including saas_owner
ALTER TABLE profiles
ADD CONSTRAINT profiles_role_check
CHECK (role IN ('saas_owner', 'super_admin', 'admin', 'editor', 'viewer'));

-- 3. Update <PERSON>'s role to saas_owner (if profile exists)
UPDATE profiles
SET role = 'saas_owner'
WHERE email = '<EMAIL>';

-- 4. Verify the update
SELECT id, email, role, full_name, created_at
FROM profiles
WHERE email = '<EMAIL>';

-- 5. Show all current roles for verification
SELECT role, COUNT(*) as count
FROM profiles
GROUP BY role
ORDER BY 
  CASE role
    WHEN 'saas_owner' THEN 1
    WHEN 'super_admin' THEN 2
    WHEN 'admin' THEN 3
    WHEN 'editor' THEN 4
    WHEN 'viewer' THEN 5
    ELSE 6
  END;

SELECT 'SaaS Owner role created and assigned successfully!' as status;
