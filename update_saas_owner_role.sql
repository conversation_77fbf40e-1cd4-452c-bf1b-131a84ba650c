-- Debug Blog Posts Migration
-- Run this in Supabase Dashboard > SQL Editor
-- This will help diagnose why old posts aren't showing

-- 1. Check if there are any blog posts at all
SELECT
    'Total Posts After Migration' as check_type,
    COUNT(*) as total_posts,
    COUNT(*) FILTER (WHERE status = 'published') as published_posts,
    COUNT(*) FILTER (WHERE status = 'draft') as draft_posts,
    COUNT(*) FILTER (WHERE submission_status = 'published_mbi') as mbi_posts,
    COUNT(*) FILTER (WHERE submission_status = 'private') as private_posts
FROM blog_posts;

-- 2. Show actual posts with their details
SELECT
    'Post Details' as check_type,
    id,
    title,
    status,
    submission_status,
    author_id,
    organization_id,
    created_at
FROM blog_posts
ORDER BY created_at DESC
LIMIT 10;

-- 3. Check <PERSON>'s profile again
SELECT
    'Stephen Profile' as check_type,
    id,
    email,
    role,
    full_name
FROM profiles
WHERE email = '<EMAIL>';

-- 4. Test if <PERSON> can see posts directly (bypassing <PERSON><PERSON> temporarily)
SET row_security = off;
SELECT
    'All Posts (No RLS)' as check_type,
    COUNT(*) as total_count,
    array_agg(title) as post_titles
FROM blog_posts;
SET row_security = on;

-- 5. Check what posts Stephen should be able to see with current RLS
SELECT
    'Posts Stephen Can See' as check_type,
    COUNT(*) as visible_count,
    array_agg(title) as visible_titles
FROM blog_posts
WHERE (
    status = 'published'
    OR author_id = (SELECT id FROM profiles WHERE email = '<EMAIL>')
    OR EXISTS (
        SELECT 1 FROM profiles
        WHERE profiles.id = (SELECT id FROM profiles WHERE email = '<EMAIL>')
        AND profiles.role = ANY (ARRAY['saas_owner', 'super_admin', 'admin', 'owner'])
    )
);
