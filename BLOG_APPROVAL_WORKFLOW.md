# Blog Approval Workflow

## Overview
The blog system now includes a comprehensive approval workflow to ensure content quality and maintain editorial control while allowing community contributions.

## User Roles & Permissions

### 🔍 **Viewer** (Default for new users)
- Can create blog posts
- Posts start as **Draft** or **Pending Approval**
- Cannot publish directly
- Can only edit their own unpublished posts

### ✏️ **Editor** 
- Can create blog posts
- Posts start as **Draft** or **Pending Approval**
- Cannot publish directly (needs admin approval)
- Can edit their own unpublished posts

### 👑 **Admin/Super Admin/Owner**
- Can create and publish posts directly
- Can approve/reject pending posts
- Can edit any post
- Can manage all blog content

## Post Statuses

### 📝 **Draft**
- Work in progress
- Only visible to author and admins
- Can be edited by author
- Can be submitted for approval

### ⏳ **Pending Approval**
- Submitted for review
- Visible to author and admins
- Awaiting admin decision
- Cannot be edited by author until decision made

### ✅ **Published**
- Live on the blog
- Visible to everyone
- Can only be edited by admins
- Has published_at timestamp

### ❌ **Rejected**
- Declined by admin
- Author can revise and resubmit
- Only visible to author and admins
- Can be edited by author

### 🗄️ **Archived**
- Removed from public view
- Preserved for records
- Can be restored by admins

## Workflow Process

### For New Users (Viewers)
1. **Sign up** → Automatically assigned "Viewer" role
2. **Create post** → Starts as "Draft"
3. **Submit for approval** → Changes to "Pending Approval"
4. **Admin reviews** → Approves or Rejects
5. **If approved** → Published automatically
6. **If rejected** → Author can revise and resubmit

### For Admins
1. **View pending posts** → See "Pending Approval" count in dashboard
2. **Review content** → Read post and check quality
3. **Make decision** → Approve (publish) or Reject
4. **Notify author** → System shows status change

## Dashboard Features

### Stats Cards
- **Total Posts**: All posts across all statuses
- **Published**: Live posts visible to public
- **Drafts**: Work in progress posts
- **Pending Approval**: Posts awaiting review (highlighted for admins)
- **Archived**: Removed posts

### Action Buttons
- **Approve** (✓): Publish pending post immediately
- **Reject** (✗): Send back to author for revision
- **Edit**: Modify post content
- **Archive**: Remove from public view
- **Delete**: Permanently remove post

## Benefits

### For Content Quality
- ✅ All public content is reviewed
- ✅ Maintains brand consistency
- ✅ Prevents spam or inappropriate content
- ✅ Ensures editorial standards

### For Contributors
- ✅ Easy submission process
- ✅ Clear status tracking
- ✅ Ability to revise rejected posts
- ✅ No technical barriers to contributing

### For Admins
- ✅ Centralized content review
- ✅ Clear approval workflow
- ✅ Bulk management capabilities
- ✅ Audit trail of all changes

## Technical Implementation

### Database Changes
- Added `pending_approval` and `rejected` statuses
- Updated RLS policies for proper permissions
- Enhanced role-based access control

### UI Improvements
- Color-coded status badges
- Approval action buttons for admins
- Pending count highlighting
- Clear workflow messaging

### Security Features
- Row Level Security (RLS) enforcement
- Role-based permission checking
- Audit logging for all actions
- Secure status transitions

## Getting Started

### For New Contributors
1. Sign up for an account
2. Navigate to `/admin/blog`
3. Click "New Post"
4. Write your content
5. Click "Submit for Approval"
6. Wait for admin review

### For Admins
1. Check "Pending Approval" count in dashboard
2. Review pending posts
3. Click "Approve" or "Reject"
4. Posts are automatically published when approved

This workflow ensures high-quality content while maintaining an open, collaborative blogging environment.
